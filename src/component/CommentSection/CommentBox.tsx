import React, {useState} from 'react';
import {
  View,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {Attachment, Attachments, SendIcon} from '../../assets/svgIcons';
import {Fonts} from '../../utilities/theme/theme';

interface Props {
  comments: string[];
  setComments: (val: string[]) => void;
}
const CommentBox: React.FC<Props> = ({comments, setComments}) => {
  const [text, setText] = useState('');

  const handleSend = () => {
    if (text.trim()) {
      setComments([text, ...comments]);
      setText('');
    }
  };
  return (
    <View style={styles.container}>
      <Image
        source={{
          uri: 'https://images.unsplash.com/photo-1740252117070-7aa2955b25f8?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXZhdGFyfGVufDB8fDB8fHww',
        }} // Replace with your avatar image
        style={styles.avatar}
      />
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder="Write a comment..."
          placeholderTextColor={'#98A2B3'}
          value={text}
          onChangeText={setText}
        />
        <TouchableOpacity style={styles.iconButton}>
          <Attachments />
        </TouchableOpacity>
        <TouchableOpacity style={styles.iconButton} onPress={handleSend}>
          <SendIcon />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CommentBox;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 12,
  },
  commentsContainer: {
    flex: 1,
    marginBottom: 10,
  },
  commentText: {
    marginVertical: 4,
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#EAECF0',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 10,
    backgroundColor: '#F9FAFB',
    height: 44,
    flex: 1,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 44,
  },
  input: {
    flex: 1,
    fontSize: 12,
    fontFamily: Fonts.Medium,
    color: '#98A2B3',
  },
  iconButton: {
    paddingHorizontal: 6,
  },
});
