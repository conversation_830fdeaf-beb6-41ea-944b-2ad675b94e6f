import {StyleSheet, Text, View, Image} from 'react-native';
import React from 'react';
import {IComment} from '../../interfaces/ITask';
import {Colors, Fonts} from '../../utilities/theme/theme';
import moment from 'moment';

interface Props {
  comment: IComment;
  senderName: string;
  senderImage?: string;
}

const CommentMessages: React.FC<Props> = ({
  comment,
  senderName,
  senderImage,
}) => {
  const isManager = comment.senderType === 'manager';

  return (
    <View style={styles.container}>
      <Image
        source={{
          uri:
            senderImage ||
            'https://images.unsplash.com/photo-1740252117070-7aa2955b25f8?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXZhdGFyfGVufDB8fDB8fHww',
        }}
        style={styles.avatar}
      />
      <View style={styles.messageContainer}>
        <View style={styles.header}>
          <Text style={styles.senderName}>{senderName}</Text>
          <Text style={styles.senderRole}>
            {isManager ? 'Manager' : 'Staff'}
          </Text>
          <Text style={styles.timestamp}>
            {moment(comment.createdAt.toDate()).format('DD MMM YYYY h:mm A')}
          </Text>
        </View>
        <Text style={styles.message}>{comment.message}</Text>
      </View>
    </View>
  );
};

export default CommentMessages;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  messageContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  senderName: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: Colors.primary_text,
    marginRight: 8,
  },
  senderRole: {
    fontSize: 12,
    fontFamily: Fonts.Medium,
    color: '#D4A574',
    marginRight: 8,
  },
  timestamp: {
    fontSize: 10,
    fontFamily: Fonts.Regular,
    color: '#98A2B3',
    marginLeft: 'auto',
  },
  message: {
    fontSize: 12,
    fontFamily: Fonts.Regular,
    color: '#475467',
    lineHeight: 18,
  },
});
