import React, {useContext, useEffect, useState} from 'react';
import {StyleSheet, Text, View, ActivityIndicator} from 'react-native';
import {IComment, ITask} from '../../interfaces/ITask';
import CommentMessages from './CommentMessages';
import CommentBox from './CommentBox';
import {AuthContext} from '../../../App';
import {Colors, Fonts} from '../../utilities/theme/theme';
import firestore from '@react-native-firebase/firestore';
import {getUserById} from '../../helpers/getUserById';
import {IUser} from '../../interfaces/IUser';
import {
  subscribeToTaskComments,
  addCommentToTask,
} from '../../helpers/commentHelpers';

interface Props {
  taskId: string;
  taskDetails: ITask;
}

const CommentSection: React.FC<Props> = ({taskId, taskDetails}) => {
  const {userData, userId} = useContext(AuthContext);
  const [comments, setComments] = useState<IComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [userCache, setUserCache] = useState<Record<string, IUser>>({});

  // Subscribe to real-time comments when component mounts
  useEffect(() => {
    setLoading(true);

    const unsubscribe = subscribeToTaskComments(taskId, newComments => {
      setComments(newComments);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [taskId]);

  // Fetch user data for each comment sender
  useEffect(() => {
    const fetchUsers = async () => {
      const userIds = new Set<string>();

      // Add manager and staff IDs
      if (taskDetails.managerId) userIds.add(taskDetails.managerId);
      if (taskDetails.staffId) userIds.add(taskDetails.staffId);

      // Add comment sender IDs
      comments.forEach(comment => {
        if (comment.senderId) userIds.add(comment.senderId);
      });

      // Fetch user data for each unique ID
      const newUserCache = {...userCache};

      for (const id of Array.from(userIds)) {
        if (!newUserCache[id]) {
          const user = await getUserById(id);
          if (user) {
            newUserCache[id] = user;
          }
        }
      }

      setUserCache(newUserCache);
    };

    if (comments.length > 0) {
      fetchUsers();
    }
  }, [comments, taskDetails]);

  const handleSendComment = async (message: string) => {
    try {
      // Create new comment
      const newComment: IComment = {
        id: Math.random().toString(36).substring(2, 15),
        message,
        senderId: userId,
        senderType: userData.userType === 'manager' ? 'manager' : 'staff',
        createdAt: firestore.Timestamp.now(),
      };

      // Use helper function to add comment
      const success = await addCommentToTask(taskId, newComment);

      if (!success) {
        console.error('Failed to add comment');
      }
    } catch (error) {
      console.error('Error sending comment:', error);
    }
  };

  const getSenderName = (senderId: string): string => {
    return userCache[senderId]?.name || 'Unknown User';
  };

  const getSenderImage = (senderId: string): string | undefined => {
    return userCache[senderId]?.profileImage?.url;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Activity</Text>

      {loading ? (
        <ActivityIndicator color={Colors.buttonbgcolor} style={styles.loader} />
      ) : comments.length === 0 ? (
        <Text style={styles.emptyText}>No comments yet</Text>
      ) : (
        <View style={styles.commentsContainer}>
          {comments.map(comment => (
            <CommentMessages
              key={comment.id}
              comment={comment}
              senderName={getSenderName(comment.senderId)}
              senderImage={getSenderImage(comment.senderId)}
            />
          ))}
        </View>
      )}

      <CommentBox
        onSendComment={handleSendComment}
        currentUserImage={userData?.profileImage?.url}
      />
    </View>
  );
};

export default CommentSection;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    paddingTop: 16,
    paddingBottom: 16,
  },
  sectionTitle: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: Colors.primary_text,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  commentsContainer: {
    marginBottom: 16,
  },
  loader: {
    marginVertical: 20,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Medium,
    color: Colors.placeholder,
    marginVertical: 20,
  },
});
