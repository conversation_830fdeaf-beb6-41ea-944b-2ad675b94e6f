import React, {useContext, useEffect, useState} from 'react';
import {StyleSheet, Text, View, ActivityIndicator} from 'react-native';
import {IComment, ITask} from '../../interfaces/ITask';
import CommentMessages from './CommentMessages';
import CommentBox from './CommentBox';
import {AuthContext} from '../../../App';
import {Colors, Fonts} from '../../utilities/theme/theme';
import firestore from '@react-native-firebase/firestore';
import {getUserById} from '../../helpers/getUserById';
import {IUser} from '../../interfaces/IUser';
import {
  subscribeToTaskComments,
  addCommentToTask,
} from '../../backend/comments';
import {images} from '../../assets/images';

interface Props {
  taskDetails: ITask;
}

const CommentSection: React.FC<Props> = ({taskDetails}) => {
  const {userData, userId} = useContext(AuthContext);
  const [comments, setComments] = useState<IComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [userCache, setUserCache] = useState<Record<string, IUser>>({});
  const [taskId, setTaskId] = useState<string>(taskDetails.id);

  // Subscribe to real-time comments when component mounts
  useEffect(() => {
    setLoading(true);

    const unsubscribe = subscribeToTaskComments(taskId, newComments => {
      setComments(newComments);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [taskId]);

  // Fetch user data for each comment sender
  useEffect(() => {
    const fetchUsers = async () => {
      const userIds = new Set<string>();

      // Add manager and staff IDs
      if (taskDetails.managerId) userIds.add(taskDetails.managerId);
      if (taskDetails.staffId) userIds.add(taskDetails.staffId);

      // Add comment sender IDs
      comments.forEach(comment => {
        if (comment.senderId) userIds.add(comment.senderId);
      });

      // Fetch user data for each unique ID
      const newUserCache = {...userCache};

      for (const id of Array.from(userIds)) {
        if (!newUserCache[id]) {
          const user = await getUserById(id);
          if (user) {
            newUserCache[id] = user;
          }
        }
      }

      setUserCache(newUserCache);
    };

    if (comments.length > 0) {
      fetchUsers();
    }
  }, [comments, taskDetails]);

  const handleSendComment = async (message: string) => {
    try {
      // Create new comment (without id since Firestore will generate it)
      const newComment = {
        message,
        senderId: userId,
        createdAt: firestore.Timestamp.now(),
      };

      // Use helper function to add comment
      const success = await addCommentToTask(taskId, newComment);

      if (!success) {
        console.error('Failed to add comment');
      }
    } catch (error) {
      console.error('Error sending comment:', error);
    }
  };

  const getSenderData = (senderId: string): IUser => {
    return (
      userCache[senderId] || {
        id: senderId,
        name: '',
        email: '',
        userType: '',
      }
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Activity</Text>

      {loading ? (
        <ActivityIndicator color={Colors.buttonbgcolor} style={styles.loader} />
      ) : comments.length === 0 ? (
        <Text style={styles.emptyText}>No activity yet</Text>
      ) : (
        <View style={styles.commentsContainer}>
          {comments.map(comment => (
            <CommentMessages
              key={comment.id}
              comment={comment}
              senderData={getSenderData(comment.senderId)}
            />
          ))}
        </View>
      )}

      <CommentBox
        onSendComment={handleSendComment}
        currentUserImage={
          userData?.profileImage?.url || String(images.avatarPlaceholder)
        }
      />
    </View>
  );
};

export default CommentSection;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    paddingTop: 16,
    paddingBottom: 16,
  },
  sectionTitle: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: Colors.primary_text,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  commentsContainer: {
    marginBottom: 16,
  },
  loader: {
    marginVertical: 20,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Medium,
    color: Colors.placeholder,
    marginVertical: 20,
  },
});
