import firestore from '@react-native-firebase/firestore';
import {IComment} from '../interfaces/ITask';

export const addCommentToTask = async (
  taskId: string,
  comment: IComment,
): Promise<boolean> => {
  try {
    const taskRef = firestore().collection('Tasks').doc(taskId);
    
    await taskRef.update({
      comments: firestore.FieldValue.arrayUnion(comment),
      updatedAt: firestore.FieldValue.serverTimestamp(),
    });
    
    return true;
  } catch (error) {
    console.error('Error adding comment to task:', error);
    return false;
  }
};

export const getTaskComments = async (taskId: string): Promise<IComment[]> => {
  try {
    const taskDoc = await firestore().collection('Tasks').doc(taskId).get();
    
    if (taskDoc.exists) {
      const taskData = taskDoc.data();
      return taskData?.comments || [];
    }
    
    return [];
  } catch (error) {
    console.error('Error fetching task comments:', error);
    return [];
  }
};

export const subscribeToTaskComments = (
  taskId: string,
  callback: (comments: IComment[]) => void,
) => {
  return firestore()
    .collection('Tasks')
    .doc(taskId)
    .onSnapshot(
      doc => {
        if (doc.exists) {
          const taskData = doc.data();
          const comments = taskData?.comments || [];
          callback(comments);
        }
      },
      error => {
        console.error('Error subscribing to task comments:', error);
      },
    );
};
