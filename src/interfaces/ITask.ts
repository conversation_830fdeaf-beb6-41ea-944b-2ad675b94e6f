import {FirebaseFirestoreTypes} from '@react-native-firebase/firestore';

interface IAttachment {
  url: string;
  path: string;
}
export interface ITask {
  id: string;
  title: string;
  note: string;
  duration: string;
  durationTimeStamp: FirebaseFirestoreTypes.Timestamp;
  createdAt: FirebaseFirestoreTypes.Timestamp;
  updatedAt: FirebaseFirestoreTypes.Timestamp;
  attachments: IAttachment[]; // Structured attachments
  issueId: string;
  managerId: string;
  staffId: string;
  priority: 'Low' | 'Medium' | 'High';
  status: string;
}
