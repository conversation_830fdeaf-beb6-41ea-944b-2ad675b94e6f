import {Platform, StyleSheet} from 'react-native';

export const Colors = {
  primary: '#000B23',
  secondary: '#FFFFFF',
  background: '#F5F5F5',
  lightcolor: '#7B7B7B',
  white: '#FFFFFF',
  buttonbgcolor: '#BE935E',
  red: '#ff0000',
  tintColor: '#617D8A',
  green: '#48D25E',
  borderColor: '#D9D9D9',
  subTextlightColor: '#FAFAFA',
  policyText: '#2C2C2E',
  policyContainer: '#E7E7E7',
  placeholder: '#93909080',
  subTextColor: '#606060',
  black: '#000000',
  primary_text: '#101828',
};

export const Fonts = {
  Bold: 'Poppins-Bold',
  Semibold: 'Poppins-SemiBold',
  Medium: 'Poppins-Medium',
  Regular: 'Poppins-Regular',
};

export const sizes = {
  paddingHorizontal: 24,
  borderRadius: 12,
};

export const appStyles = StyleSheet.create({
  headerTitleStyle: {
    color: Colors.primary,
    fontSize: 14,
    fontFamily: Fonts.Semibold,
  },
  headerStyle: {backgroundColor: Colors.background},
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flexSpaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.black,
    fontFamily: Fonts.Semibold,
    textAlign: 'center',
    marginTop: 42,
  },
  bottomButtonContainer: {
    backgroundColor: Colors.white,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 14,
    paddingBottom: Platform.OS == 'ios' ? 6 : 14,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,

    elevation: 12,
  },
  h2: {
    fontSize: 20,
    color: Colors.primary,
    fontFamily: Fonts.Bold,
  },
  h4: {
    fontSize: 16,
    color: Colors.black,
    fontFamily: Fonts.Semibold,
  },
  h5: {
    fontSize: 14,
    color: Colors.primary,
    fontFamily: Fonts.Semibold,
  },
  h6: {
    fontSize: 12,
    color: Colors.primary,
    fontFamily: Fonts.Semibold,
  },
  body4: {
    fontSize: 14,
    color: Colors.lightcolor,
    fontFamily: Fonts.Medium,
  },
  body5: {
    fontSize: 12,
    color: Colors.lightcolor,
    fontFamily: Fonts.Regular,
  },
  body6: {
    fontSize: 10,
    color: Colors.lightcolor,
    fontFamily: Fonts.Regular,
  },
});
